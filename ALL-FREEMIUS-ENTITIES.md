# Official Freemius API entities & and their data + fields + types

## Product

API response:

```json
{
  "secret_key": "sk_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
  "public_key": "pk_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
  "id": "123456",
  "created": "2025-01-01 00:00:00",
  "updated": "2025-01-01 00:00:00",
  "parent_plugin_id": "123456",
  "developer_id": "123456",
  "store_id": "123456",
  "slug": "my-freemius-plugin",
  "title": "My Freemius Plugin",
  "environment": 0,
  "icon": "https://img.freemius.com/plugin-icon-blue.png",
  "default_plan_id": "string",
  "plans": "123456,123457,123458",
  "features": "123456,123457,123458",
  "money_back_period": 0,
  "refund_policy": "flexible",
  "annual_renewals_discount": 0,
  "renewals_discount_type": "percentage",
  "is_released": true,
  "is_sdk_required": true,
  "is_pricing_visible": true,
  "is_wp_org_compliant": true,
  "installs_count": 0,
  "active_installs_count": 0,
  "free_releases_count": 0,
  "premium_releases_count": 0,
  "total_purchases": 0,
  "total_subscriptions": 0,
  "total_renewals": 0,
  "total_failed_purchases": "1234",
  "earnings": "1234.56",
  "type": "plugin",
  "is_static": true
}
```

* `Body`: application/json

- `secret_key` *string*
The secret key associated with the entity for authorization.
_Example_: "sk_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"

- `public_key` *string*
The public key associated with the entity for authorization.
_Example_: "pk_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"

- `id` *string* *(int64)* *>= 1*
The unique identifier of the entity.
_Example_: "123456"

- `created` *string* *(date-time)*
The date and time the entity was created, under UTC timezone.
_Example_: "2025-01-01 00:00:00"

- `updated` *string or null* *(date-time)*
The date and time the entity was updated, under UTC timezone. If `null` then the entity was never updated since its creation.
_Example_: "2025-01-01 00:00:00"

- `parent_plugin_id` *string or null* *(int64)* *>= 1*
If the product is an add-on then this is the ID of the parent product.
_Example_: "123456"

- `developer_id` *string* *(int64)* *>= 1*
The ID of the developer that owns the product.
_Example_: "123456"

- `store_id` *string* *(int64)* *>= 1*
The ID of the store that the product is being sold on.
_Example_: "123456"

- `slug` *string*
The `slug` of the product. If your plugin is listed on `WordPress.org` repository, use the exact slug.
_Example_: "my-freemius-plugin"

- `title` *string*
The title of the product.
_Example_: "My Freemius Plugin"

- `environment` *number*
The environment the entity belongs to. 0 means it belongs to the production environment, 1 means it belongs to the sandbox environment.
Enum "1" "0"
_Example_: 0

- `icon` *string or null*
Product's icon (profile picture).
_Example_: "https://img.freemius.com/plugin-icon-blue.png"


- `default_plan_id` *string** (int64)*
Default plan ID of the product.

- `plans` *string*
Comma separated, ordered plans collection.
_Example_: "123456,123457,123458"

- `features` *string*
Comma separated, ordered features collection.
_Example_: "123456,123457,123458"

- `money_back_period` *integer*
Money-back guarantee in days.

- `refund_policy` *string*
Enum "flexible" "moderate" "strict"

- `annual_renewals_discount` *integer or null*
Renewals discount that will be applied to the chosen plan.

- `renewals_discount_type` *string*
The type of renewals discount, percentage or dollar.
Enum "percentage" "dollar"

- `is_released` *boolean*
A flag that controls the visibility of add-ons in the in-dashboard add-ons marketplace. Defaults to true. Only applicable if the product is an add-on.

- `is_sdk_required` *boolean*
A flag that controls whether the SDK should be required or not during deployment of a version. It defaults to `true`.

- `is_pricing_visible` *boolean*
Determines if the pricing should be visible in the in-SDK pricing page. Defaults to true. Turn this off during the development of a new product.

- `is_wp_org_compliant` *boolean*
Set to true if the free version of the module is hosted on WordPress.org. Defaults to true.

- `installs_count` *integer*
Total number of opted in sites which were logged with the SDK.

- `active_installs_count` *integer*
Total number of active sites where the SDK is active.

- `free_releases_count` *integer*
The number of "free" version of the product that were deployed from Freemius.premium_releases_count*integer*
The number of "premium" version of the product that were deployed from Freemius.

- `total_purchases` *integer*
Total number of payments recorded for the product.

- `total_subscriptions` *integer*
Total number of subscriptions recorded for the product.

- `total_renewals` *integer*
Total number of renewals recorded for the product.

- `total_failed_purchases` *integer*
Total number of failed payments recorded for the product.
_Example_: "1234"

- `earnings` *number**(float)*
Total gross revenues.
_Example_: "1234.56"

- `type` *string*
Enum "plugin" "theme" "widget" "template"

- `is_static` *boolean*
Determines whether the product is categorized as a static product (for example, a widget or a template).


## Subscription

API response:

```json
{
  "user_id": "123456",
  "install_id": "123456",
  "plan_id": "123456",
  "pricing_id": "123456",
  "license_id": "123456",
  "ip": "0.0.0.0",
  "country_code": "us",
  "zip_postal_code": "92710",
  "vat_id": "**********",
  "coupon_id": "123456",
  "user_card_id": "12345",
  "source": 0,
  "plugin_id": "123456",
  "external_id": "abcde12345",
  "gateway": "string",
  "environment": 0,
  "id": "123456",
  "created": "2025-01-01 00:00:00",
  "updated": "2025-01-01 00:00:00",
  "currency": "usd",
  "tax_rate": "1.00",
  "total_gross": "1.21",
  "amount_per_cycle": "1.00",
  "initial_amount": "1.00",
  "renewal_amount": "1.00",
  "renewals_discount": "1",
  "renewals_discount_type": "percentage",
  "billing_cycle": 1,
  "outstanding_balance": "1.00",
  "failed_payments": "1",
  "trial_ends": "2025-01-01 00:00:00",
  "next_payment": "2025-01-01 00:00:00",
  "canceled_at": "2025-01-01 00:00:00"
}
```

* `Body`: application/json

- `user_id` *string* *(int64)* *>= 1*
The ID of the user the entity belongs to.
_Example_: "123456"

- `install_id` *string or null* *(int64)* *>= 1*
The ID of the installation or site the entity is associated with, a `null` value means it has not been associated with an installation yet.
_Example_: "123456"

- `plan_id` *string* *(int64)* *>= 1*
The ID of the plan associated with the entity.
_Example_: "123456"

- `pricing_id` *string or null* *(int64)* *>= 1*
The ID of the pricing associated with the entity.
_Example_: "123456"

- `license_id` *string* *(int64)* *>= 1*
The ID of the license associated with the entity.
_Example_: "123456"

- `ip` *string or null* *(ipv4|ipv6)*
The IP address associated with the entity.
_Example_: "0.0.0.0"

- `country_code` *string*
The [ISO 3166-1 alpha 2](http://www.wikiwand.com/en/ISO_3166-1_alpha-2) two-letter country code associated with the entity.
_Example_: "us"

- `zip_postal_code` *string or null*
The postal/zip code of the location.
_Example_: "92710"

- `vat_id` *string or null*
The business VAT number (EU or UK territories) or other tax ID (for example Sales Tax ID for the US).
_Example_: "**********"

- `coupon_id` *string or null* *(int64)* *>= 1*
The ID of the coupon associated with the entity.
_Example_: "123456"

- `user_card_id` *string* *(int64)*
The ID of the user card that was used for this payment.
_Example_: "12345"

- `source` *number*
The source of the migration data. To get support migrating from other platform please see our [documentation](https://freemius.com/help/documentation/migration/).
    - `0` - Freemius
    - `1` - Other
    - `2` - Easy Digital Downloads (EDD)
    - `3` - WooCommerce (WC)
    - `4` - Rating Widget
    - `5` - Gumroad
    - `6` - CodeCanyon
    - `7` - ThemeForest
    - `8` - AppSumo
    - `9` - SendOwl
    - `10` - WHMCS
    - `11` - Lemon Squeezy
Enum "0" "1" "2" "3" "4" "5" "6" "7" "8" "9" "10" "11"

- `plugin_id` *string* *(int64)* *>= 1*
The ID of the product the entity belongs to.
_Example_: "123456"

- `external_id` *string*
The external ID of the gateway entity.
_Example_: "abcde12345"

- `gateway` *string or null*
The gateway used for the purchase. The gateway will be set to `null` when purchasing a product with a 100% discount.

- `environment` *number*
The environment the entity belongs to. 0 means it belongs to the production environment, 1 means it belongs to the sandbox environment.
Enum "1" "0"
_Example_: 0

- `id` *string* *(int64)* *>= 1*
The unique identifier of the entity.
_Example_: "123456"

- `created` *string* *(date-time)*
The date and time the entity was created, under UTC timezone.
_Example_: "2025-01-01 00:00:00"

- `updated` *string or null* *(date-time)*
The date and time the entity was updated, under UTC timezone. If `null` then the entity was never updated since its creation.
_Example_: "2025-01-01 00:00:00"

- `currency` *string* *= 3 characters*
3-char currency code.
Enum "usd" "eur" "gbp"

- `tax_rate` *number* *(float)* *>= 0*
The tax rate as a fraction. It will either be US sales tax or VAT.
_Example_: "1.00"

- `total_gross` *number* *(float)* *>= 0*
The total gross amount of the subscription, including taxes.
_Example_: "1.21"

- `amount_per_cycle` *number* *(float)* *>= 0*
The plan's original amount per cycle (not including taxes).
_Example_: "1.00"

- `initial_amount` *number* *(float)* *>= 0*
The initial payment amount (not including taxes).
_Example_: "1.00"

- `renewal_amount` *number* *(float)* *>= 0*
The renewals amount (not including taxes).
_Example_: "1.00"

- `renewals_discount` *integer* *>= 0*
The renewals discount that will be applied to the chosen plan.
_Example_: "1"

- `renewals_discount_type` *string*
The type of renewals discount, percentage or dollar.
Enum "percentage" "dollar"

- `billing_cycle` *number*
The billing cycle of the subscription in number of months. 1 means monthly, 12 means annually, 0 means lifetime usually when subscriptions are created for lifetime trials.
Enum "1" "12" "0"

- `outstanding_balance` *number* *(float)* *>= 0*
Any outstanding balance that the user has for this subscription.
_Example_: "1.00"

- `failed_payments` *integer* *>= 0*
Number of failed payments associated with the subscription.
_Example_: "1"

- `trial_ends` *string or null* *(date-time)*
The date time when the trial period ends. If `null` the subscription is not associated with a trial.
_Example_: "2025-01-01 00:00:00"

- `next_payment` *string or null* *(date-time)*
Datetime of the next payment, or `null` if cancelled.
_Example_: "2025-01-01 00:00:00"

- `canceled_at` *string or null* *(date-time)*
Datetime of the cancellation.
_Example_: "2025-01-01 00:00:00"


## Installation

API response:

```json
{
  "secret_key": "sk_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
  "public_key": "pk_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
  "id": "123456",
  "created": "2025-01-01 00:00:00",
  "updated": "2025-01-01 00:00:00",
  "site_id": 1234,
  "plugin_id": "123456",
  "user_id": "123456",
  "url": "https://example.com",
  "title": "Catwalk Designs",
  "version": "1.0.0",
  "plan_id": "1234",
  "license_id": "123456",
  "trial_plan_id": "1234",
  "trial_ends": "2025-01-01 00:00:00",
  "subscription_id": "123456",
  "gross": 100,
  "country_code": "us",
  "language": "en-GB",
  "platform_version": "1.0.0",
  "sdk_version": "1.2.2",
  "programming_language_version": "5.6",
  "is_active": true,
  "is_disconnected": true,
  "is_premium": true,
  "is_uninstalled": true,
  "is_locked": true,
  "source": 0,
  "upgraded": "2025-01-01 00:00:00",
  "last_seen_at": "2025-01-01 00:00:00",
  "last_served_update_version": "1.2.2",
  "is_beta": true
}
```

* `Body`: application/json

- `secret_key` *string*
The secret key associated with the entity for authorization.
_Example_: "sk_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"

- `public_key` *string*
The public key associated with the entity for authorization.
_Example_: "pk_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"

- `id` *string* *(int64)* *>= 1*
The unique identifier of the entity.
_Example_: "123456"

- `created` *string* *(date-time)*
The date and time the entity was created, under UTC timezone.
_Example_: "2025-01-01 00:00:00"

- `updated` *string or null* *(date-time)*
The date and time the entity was updated, under UTC timezone. If `null` then the entity was never updated since its creation.
_Example_: "2025-01-01 00:00:00"

- `site_id` *string* *(int64)*
The ID of the site.
_Example_: "1234"

- `plugin_id` *string* *(int64)* *>= 1*
The ID of the product the entity belongs to.
_Example_: "123456"

- `user_id` *string* *(int64)* *>= 1*
The ID of the user the entity belongs to.
_Example_: "123456"

- `url` *string or null*
The site URL.
_Example_: "https://example.com"

- `title` *string or null*
The site title.
_Example_: "Catwalk Designs"

- `version` *string*
The Product version.
_Example_: "1.0.0"

- `plan_id` *string* *(int64)* *>= 1*
The ID of the plan associated with the product that the install has a license activation. If `null` it means the install is using the free plan.
_Example_: "1234"

- `license_id` *string or null* *(int64)* *>= 1*
The ID of the license associated with the entity.
_Example_: "123456"

- `trial_plan_id` *string* *(int64)* *>= 1*
The ID of the trial license associated to the product. If this is not a trial, this will be `null`.
_Example_: "1234"

- `trial_ends` *string or null* *(date-time)*
The product trial license expiry date. If this is not a trial, this will be null.
_Example_: "2025-01-01 00:00:00"

- `subscription_id` *string or null* *(int64)* *>= 1*
The ID of the subscription associated with the entity.
_Example_: "123456"

- `gross` *number* *(float)*
The gross amount the install has spent on the product. This includes one time purchase, or subscriptions and renewals.
_Example_: 100

- `country_code` *string or null*
The [ISO 3166-1 alpha 2](http://www.wikiwand.com/en/ISO_3166-1_alpha-2) two-letter country code associated with the entity.
_Example_: "us"

- `language` *string or null*
The language specified for the product install.
_Example_: "en-GB"

- `platform_version` *string or null*
The platform version (e.g WordPress version).
_Example_: "1.0.0"

- `sdk_version` *string or null*
The Freemius SDK version. Only relevant for WordPress products using the Freemius [WP SDK](https://freemius.com/help/documentation/wordpress-sdk/).
_Example_: "1.2.2"

- `programming_language_version` *string or null*
The programming language version (e.g PHP version).
_Example_: "5.6"

- `is_active` *boolean*
If the product is actively installed on the site.

- `is_disconnected` *boolean*
If the product is disconnected on the site.

- `is_premium` *boolean*
If the install using the premium code. Relevant only for WP Products.

- `is_uninstalled` *boolean*
If the product is uninstalled on the site.

- `is_locked` *boolean*
If the product is locked on the site.

- `source` *number*
The source of the migration data. To get support migrating from other platform please see our [documentation](https://freemius.com/help/documentation/migration/).
    - `0` - Freemius
    - `1` - Other
    - `2` - Easy Digital Downloads (EDD)
    - `3` - WooCommerce (WC)
    - `4` - Rating Widget
    - `5` - Gumroad
    - `6` - CodeCanyon
    - `7` - ThemeForest
    - `8` - AppSumo
    - `9` - SendOwl
    - `10` - WHMCS
    - `11` - Lemon Squeezy
Enum "0" "1" "2" "3" "4" "5" "6" "7" "8" "9" "10" "11"

- `upgraded` *string or null* *(date-time)*
Time when the product was upgraded to the current version. If never upgraded since the initial installation, this will be `null`.
_Example_: "2025-01-01 00:00:00"

- `last_seen_at` *string or null* *(date-time)*
The last time the product was used on the site.
_Example_: "2025-01-01 00:00:00"

- `last_served_update_version` *string or null*
The last product version update used on the site. If not updated, this will be null.
_Example_: "1.2.2"

- `is_beta` *boolean*
Whether the install is participating in the beta program.


## Plan

API response:

```json
{
  "id": "123456",
  "created": "2025-01-01 00:00:00",
  "updated": "2025-01-01 00:00:00",
  "plugin_id": "123456",
  "name": "professional",
  "title": "Professional",
  "description": "For small to medium businesses.",
  "is_free_localhost": true,
  "is_block_features": true,
  "is_block_features_monthly": true,
  "license_type": 0,
  "trial_period": "14",
  "is_require_subscription": true,
  "support_kb": "https://example.com/help/documentation",
  "support_forum": "https://example.com/support",
  "support_email": "<EMAIL>",
  "support_phone": "************",
  "is_success_manager": true,
  "is_featured": true,
  "is_hidden": true
}
```

* `Body`: application/json

- `id` *string* *(int64)* *>= 1*
The unique identifier of the entity.
_Example_: "123456"

- `created` *string* *(date-time)*
The date and time the entity was created, under UTC timezone.
_Example_: "2025-01-01 00:00:00"

- `updated` *string or null* *(date-time)*
The date and time the entity was updated, under UTC timezone. If `null` then the entity was never updated since its creation.
_Example_: "2025-01-01 00:00:00"

- `plugin_id` *string* *(int64)* *>= 1*
The ID of the product the entity belongs to.
_Example_: "123456"

- `name` *string* *(slug)*
The name of the plan. Only lowercase characters allowed.
_Example_: "professional"

- `title` *string*
The title of the plan. This is the human readable name of the plan. Please do not add the suffix `Plan` to the title, as Freemius does that for you at various places.
_Example_: "Professional"

- `description` *string or null*
The description of the plan.
_Example_: "For small to medium businesses."

- `is_free_localhost` *boolean*
Whether the plan offers unlimited local or staging activations with the same license.

- `is_block_features` *boolean*
Whether to block features on expiration of **annual** licenses. If `false`, does not block features but only block updates and support.

- `is_block_features_monthly` *boolean*
Whether to block particular features on expiration of **monthly** licenses. If `false`, does not block features but only block updates and support.

- `license_type` *integer*
The type of the license. `0` for per domain license. `1` for per subdomain license.
Enum "0" "1"

- `trial_period` *integer or null* *>= 1*
The number of days a trial period will last. If `null` the plan does not support trials.
_Example_: "14"

- `is_require_subscription` *boolean*
Whether to require a subscription payment for the trial period.

- `support_kb` *string or null*
The Knowledge Base URL.
_Example_: "https://example.com/help/documentation"

- `support_forum` *string or null*
The support Forum URL.
_Example_: "https://example.com/support"

- `support_email` *string or null*
The support email address.
_Example_: "<EMAIL>"

- `support_phone` *string or null*
The support phone contact.
_Example_: "************"

- `is_success_manager` *boolean*
Is a personal success manager allocated with the plan.

- `is_featured` *boolean*
Whether this is a featured plan.

- `is_hidden` *boolean*
Whether to hide the plan from the auto-generated pricing page. Mostly relevant for WordPress products and our WordPress SDK. Please see our [pricing-page](https://github.com/Freemius/pricing-page/) library to implement your own.


## License

API response:

```json
{
  "id": "123456",
  "created": "2025-01-01 00:00:00",
  "updated": "2025-01-01 00:00:00",
  "plugin_id": "123456",
  "user_id": "123456",
  "plan_id": "123456",
  "pricing_id": "123456",
  "quota": 10,
  "activated": 1,
  "activated_local": 1,
  "expiration": "2025-12-31 23:59:59",
  "secret_key": "sk_123FGqM456Pa786WtOp%^+67Y+;sXXz",
  "is_free_localhost": true,
  "is_block_features": true,
  "is_cancelled": true,
  "is_whitelabeled": true,
  "environment": 0,
  "source": 0
}
```

* `Body`: application/json

- `id` *string* *(int64)* *>= 1*
The unique identifier of the entity.
_Example_: "123456"

- `created` *string* *(date-time)*
The date and time the entity was created, under UTC timezone.
_Example_: "2025-01-01 00:00:00"

- `updated` *string or null* *(date-time)*
The date and time the entity was updated, under UTC timezone. If `null` then the entity was never updated since its creation.
_Example_: "2025-01-01 00:00:00"

- `plugin_id` *string* *(int64)* *>= 1*
The ID of the product the entity belongs to.
_Example_: "123456"

- `user_id` *string or null* *(int64)* *>= 1*
The ID of the user the entity belongs to. If NULL then still not associated to any user.
_Example_: "123456"

- `plan_id` *string* *(int64)* *>= 1*
The ID of the plan associated with the entity.
_Example_: "123456"

- `pricing_id` *string or null* *(int64)* *>= 1*
The ID of the pricing associated with the entity.
_Example_: "123456"

- `quota` *integer or null*
The maximum number of license activations. If `null` then the license will support unlimited activations.
_Example_: 10

- `activated` *integer*
The total number of production activation the license has. This does not count local or staging environment activations.
_Example_: 1

- `activated_local` *integer or null*
The number of times the license is activated on local or staging environments.
_Example_: 1

- `expiration` *string or null* *(date-time)*
The expiration date of the license. If `null` then it's a lifetime license.
_Example_: "2025-12-31 23:59:59"

- `secret_key` *string*
The license key. This is used for activating the license on the user's site.
_Example_: "sk_123FGqM456Pa786WtOp%^+67Y+;sXXz"

- `is_free_localhost` *boolean*
Whether the license offers unlimited local or staging environment activations.
_Default_: true

- `is_block_features` *boolean*
Whether to block features after expiration of the license. If set to `false`, this would not block features, would only block updates.
_Default_: true

- `is_cancelled` *boolean*
If the license is canceled from the Developer Dashboard.

- `is_whitelabeled` *boolean*
Guide the Freemius WP SDK when the product should be running in a white-label mode.

- `environment` *number*
The environment the entity belongs to. 0 means it belongs to the production environment, 1 means it belongs to the sandbox environment.
Enum "1" "0"
_Example_: 0

- `source` *number*
The source of the migration data. To get support migrating from other platform please see our documentation.
    - `0` - Freemius
    - `1` - Other
    - `2` - Easy Digital Downloads (EDD)
    - `3` - WooCommerce (WC)
    - `4` - Rating Widget
    - `5` - Gumroad
    - `6` - CodeCanyon
    - `7` - ThemeForest
    - `8` - AppSumo
    - `9` - SendOwl
    - `10` - WHMCS
    - `11` - Lemon Squeezy
Enum "0" "1" "2" "3" "4" "5" "6" "7" "8" "9" "10" "11"

## User

API response:

```json
{
  "note": "string",
  "is_marketing_allowed": true,
  "is_beta": true,
  "email": "<EMAIL>",
  "first": "Jane",
  "last": "Doe",
  "picture": "https://example.com/profile-pic.jpg",
  "ip": "127.0.0.1",
  "is_verified": "true",
  "auth": "app2fa",
  "secret_key": "sk_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
  "public_key": "pk_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
  "id": "123456",
  "created": "2025-01-01 00:00:00",
  "updated": "2025-01-01 00:00:00",
  "gross": 0.1,
  "last_login_at": "2025-07-30 05:56:29",
  "email_status": "delivered"
}
```

* `Body`: application/json

- `note` *string*
A note about the user. Only visible to the developer.

- `is_marketing_allowed` *boolean or null*
Whether or not the user has given their consent for marketing materials. A `null` value indicates that the user has not made a decision yet.

- `is_beta` *boolean*
Whether or not the user has opted-in to beta versions. We do not recommend using this option anymore since it will opt-in the user to all sites/activations. Currently, sites or activations can be managed individually.

- `email` *string*
Email address of the person.
_Example_: "<EMAIL>"

- `first` *string*
First name of the person.
_Example_: "Jane"

- `last` *string*
Last name of the person.
_Example_: "Doe"

- `picture` *string*
Profile picture URL.
_Example_: "https://example.com/profile-pic.jpg"

- `ip` *string or null* *(ipv4|ipv6)*
The IP address (v4 or v6).
_Example_: "127.0.0.1"

- `is_verified` *boolean*
Whether the person is trusted or not.
_Example_: "true"

- `auth` *string*
The type of authentication. If `app2fa` is set, the person has signed for 2FA authentication.
Enum "app2fa" "password"

- `secret_key` *string*
The secret key associated with the entity for authorization.
_Example_: "sk_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"

- `public_key` *string*
The public key associated with the entity for authorization.
_Example_: "pk_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"

- `id` *string* *(int64)* *>= 1*
The unique identifier of the entity.
_Example_: "123456"

- `created` *string* *(date-time)*
The date and time the entity was created, under UTC timezone.
_Example_: "2025-01-01 00:00:00"

- `updated` *string or null* *(date-time)*
The date and time the entity was updated, under UTC timezone. If `null` then the entity was never updated since its creation.
_Example_: "2025-01-01 00:00:00"

- `gross` *number* *(float)*
The total amount of money the user has spent on the platform.

- `last_login_at` *string or null* *(date-time)*
Date and time of the last login.
_Example_: "2025-07-30 05:56:29"

- `email_status` *string*
The status of the last email sent to the user.
Enum "delivered" "bounce" "dropped"

## Payment

API response:

```json
{
  "user_id": "123456",
  "install_id": "123456",
  "plan_id": "123456",
  "pricing_id": "123456",
  "license_id": "123456",
  "ip": "0.0.0.0",
  "country_code": "us",
  "zip_postal_code": "92710",
  "vat_id": "**********",
  "coupon_id": "123456",
  "user_card_id": "12345",
  "source": 0,
  "plugin_id": "123456",
  "external_id": "abcde12345",
  "gateway": "string",
  "environment": 0,
  "id": "123456",
  "created": "2025-01-01 00:00:00",
  "updated": "2025-01-01 00:00:00",
  "subscription_id": "123456",
  "gross": 2075.45,
  "bound_payment_id": "123456",
  "gateway_fee": 2.99,
  "vat": 1.24,
  "is_renewal": false,
  "type": "payment"
}
```

* `Body`: application/json

- `user_id` *string* *(int64)* *>= 1*
The ID of the user the entity belongs to.
_Example_: "123456"

- `install_id` *string or null* *(int64)* *>= 1*
The ID of the installation or site the entity is associated with, a `null` value means it has not been associated with an installation yet.
_Example_: "123456"

- `plan_id` *string* *(int64)* *>= 1*
The ID of the plan associated with the entity.
_Example_: "123456"

- `pricing_id` *string or null* *(int64)* *>= 1*
The ID of the pricing associated with the entity.
_Example_: "123456"

- `license_id` *string* *(int64)* *>= 1*
The ID of the license associated with the entity.
_Example_: "123456"

- `ip` *string or null* *(ipv4|ipv6)*
The IP address associated with the entity.
_Example_: "0.0.0.0"

- `country_code` *string*
The ISO 3166-1 alpha 2 two-letter country code associated with the entity.
_Example_: "us"

- `zip_postal_code` *string or null*
The postal/zip code of the location.
_Example_: "92710"

- `vat_id` *string or null*
The business VAT number (EU or UK territories) or other tax ID (for example Sales Tax ID for the US).
_Example_: "**********"

- `coupon_id` *string or null* *(int64)* *>= 1*
The ID of the coupon associated with the entity.
_Example_: "123456"

- `user_card_id` *string* *(int64)*
The ID of the user card that was used for this payment.
_Example_: "12345"

- `source` *number*
The source of the migration data. To get support migrating from other platform please see our documentation.
Enum "0" "1" "2" "3" "4" "5" "6" "7" "8" "9" "10" "11"

- `plugin_id` *string* *(int64)* *>= 1*
The ID of the product the entity belongs to.
_Example_: "123456"

- `external_id` *string*
The external ID of the gateway entity.
_Example_: "abcde12345"

- `gateway` *string or null*
The gateway used for the purchase. The gateway will be set to `null` when purchasing a product with a 100% discount.

- `environment` *number*
The environment the entity belongs to. 0 means it belongs to the production environment, 1 means it belongs to the sandbox environment.
Enum "1" "0"
_Example_: 0

- `id` *string* *(int64)* *>= 1*
The unique identifier of the entity.
_Example_: "123456"

- `created` *string* *(date-time)*
The date and time the entity was created, under UTC timezone.
_Example_: "2025-01-01 00:00:00"

- `updated` *string or null* *(date-time)*
The date and time the entity was updated, under UTC timezone. If `null` then the entity was never updated since its creation.
_Example_: "2025-01-01 00:00:00"

- `subscription_id` *string or null* *(int64)* *>= 1*
The ID of the subscription associated with the entity.
_Example_: "123456"

- `gross` *number* *(float)*
The payment amount (not including taxes).
_Example_: 2075.45

- `bound_payment_id` *string* *(int64)* *>= 1*
The ID of the payment that this payment is bound to, in case of a refund or chargeback/dispute.
_Example_: "123456"

- `gateway_fee` *number* *(float)*
The fee that the gateway took for processing this payment.
_Example_: 2.99

- `vat` *number*
The actual tax amount. It could be any kind of tax, not necessarily VAT. For example we support US Sales Tax.
_Example_: 1.24

- `is_renewal` *boolean*
If the payment is a renewal.

- `type` *string*
The type of the payment.
Enum "payment" "refund" "disputed" "won_dispute" "lost_dispute" "chargeback"

## Cart

API response:

```json
{
  "id": "123456",
  "created": "2025-01-01 00:00:00",
  "updated": "2025-01-01 00:00:00",
  "status": "pricing_visit",
  "mode": "dashboard",
  "payment_method": "cc",
  "plugin_id": "123456",
  "plan_id": "123456",
  "pricing_id": "123456",
  "is_trial": true,
  "billing_cycle": 1,
  "install_id": "123456",
  "coupon_id": "123456",
  "country_code": "us",
  "zip_postal_code": "92710",
  "vat_id": "**********",
  "user_id": "123456",
  "email": "<EMAIL>",
  "first": "John",
  "last": "Doe",
  "ip": "0.0.0.0",
  "url": "https://example.com/pricing",
  "environment": 0,
  "is_disabled": "false",
  "is_unsubscribed": "false",
  "visited": "2025-01-01 00:00:00",
  "completed": "2025-01-01 00:00:00",
  "price": "19.99",
  "gross": "359.64",
  "coupon_code": "BLACKFRIDAY2024",
  "licenses": "10"
}
```

* `Body`: application/json

- `id` *string* *(int64)* *>= 1*
The unique identifier of the entity.
_Example_: "123456"

- `created` *string* *(date-time)*
The date and time the entity was created, under UTC timezone.
_Example_: "2025-01-01 00:00:00"

- `updated` *string or null* *(date-time)*
The date and time the entity was updated, under UTC timezone. If `null` then the entity was never updated since its creation.
_Example_: "2025-01-01 00:00:00"

- `status` *string*
The status of the cart.
Enum "pricing_visit" "visit" "abandoned" "completed" "email_1_sent" "email_2_sent" "email_3_sent" "recovered"

- `mode` *string*
The mode the checkout app was opened in, when the cart was created. For example `dashboard`: The checkout was opened through our WordPress SDK. `dialog`: The checkout was opened through our JavaScript SDK. `page`: The checkout was opened directly.
Enum "dashboard" "dialog" "page"

- `payment_method` *string*
The payment method selected for the purchase.
Enum "cc" "paypal" "ideal"

- `plugin_id` *string* *(int64)* *>= 1*
The ID of the product the entity belongs to.
_Example_: "123456"

- `plan_id` *string or null* *(int64)* *>= 1*
The ID of the plan associated with the entity.
_Example_: "123456"

- `pricing_id` *string or null* *(int64)* *>= 1*
The ID of the pricing associated with the entity.
_Example_: "123456"

- `is_trial` *boolean*
Whether the plan is a trial.

- `billing_cycle` *number*
The billing cycle of the subscription in number of months. 1 means monthly, 12 means annually, 0 means lifetime usually when subscriptions are created for lifetime trials.
Enum "1" "12" "0"

- `install_id` *string or null* *(int64)* *>= 1*
The ID of the installation or site the entity is associated with, a `null` value means it has not been associated with an installation yet.
_Example_: "123456"

- `coupon_id` *string or null* *(int64)* *>= 1*
The ID of the coupon associated with the entity.
_Example_: "123456"

- `country_code` *string or null*
The ISO 3166-1 alpha 2 two-letter country code associated with the entity.
_Example_: "us"

- `zip_postal_code` *string*
The postal/zip code of the location.
_Example_: "92710"

- `vat_id` *string or null*
The business VAT number (EU or UK territories) or other tax ID (for example Sales Tax ID for the US).
_Example_: "**********"

- `user_id` *string or null* *(int64)* *>= 1*
The ID of the user the entity belongs to. If NULL then still not associated to any user.
_Example_: "123456"

- `email` *string*
The prospect's email address.
_Example_: "<EMAIL>"

- `first` *string*
The prospect's first name.
_Example_: "John"

- `last` *string*
The prospect's last name.
_Example_: "Doe"

- `ip` *string* *(ipv4|ipv6)*
The IP address associated with the entity.
_Example_: "0.0.0.0"

- `url` *string*
The page URL containing the checkout.
_Example_: "https://example.com/pricing"

- `environment` *number*
The environment the entity belongs to. 0 means it belongs to the production environment, 1 means it belongs to the sandbox environment.
Enum "1" "0"
_Example_: 0

- `is_disabled` *boolean*
Cart supports recovery campaign. If disabled, the recovery campaign will be stopped.

- `is_unsubscribed` *boolean*
If set to `true`, the cart recovery campaign will be non-functional and cannot be re-enabled. This happens if the prospect has clicked the "unsubscribe" link from any of the cart recovery emails.

- `visited` *string or null* *(date-time)*
The first time the prospect visited the checkout when the cart was being created.
_Example_: "2025-01-01 00:00:00"

- `completed` *string or null* *(date-time)*
The cart completion date.
_Example_: "2025-01-01 00:00:00"

- `price` *number* *(float)*
The cart's original price.
_Example_: "19.99"

- `gross` *number or null* *(float)*
Gross amount associated with the cart. (Only available when `enriched` is set to `true`.)
_Example_: "359.64"

- `coupon_code` *string or null*
Coupon code associated with the cart. (Only available when `enriched` is set to `true`.)
_Example_: "BLACKFRIDAY2024"

- `licenses` *number or null*
Number of licenses associated with the cart. (Only available when `enriched` is set to `true`.)
_Example_: "10"

## Coupon

API response:

```json
{
  "id": "123456",
  "created": "2025-01-01 00:00:00",
  "updated": "2025-01-01 00:00:00",
  "entity_id": 12345,
  "entity_type": "plugin",
  "plans": "123,654,8757",
  "licenses": "1,5,10,0",
  "billing_cycles": "1,12",
  "code": "BLACKFRIDAY2024",
  "discount": 0,
  "discount_type": "dollar",
  "start_date": "2025-04-01 11:13:28",
  "end_date": "2025-04-30 19:17:21",
  "redemptions": 0,
  "redemptions_limit": 0,
  "has_renewals_discount": true,
  "has_addons_discount": true,
  "is_one_per_user": true,
  "is_active": true,
  "user_type": "all",
  "source": 0
}
```


* `Body`: application/json

- `id` *string* *(int64)* *>= 1*
The unique identifier of the entity.
_Example_: "123456"

- `created` *string* *(date-time)*
The date and time the entity was created, under UTC timezone.
_Example_: "2025-01-01 00:00:00"

- `updated` *string or null* *(date-time)*
The date and time the entity was updated, under UTC timezone. If `null` then the entity was never updated since its creation.
_Example_: "2025-01-01 00:00:00"

- `entity_id` *string* *(int64)* *>= 1*
The ID of the entity the coupon belongs to.
_Example_: "12345"

- `entity_type` *string*
The type of the entity the coupon belongs to.
Enum "plugin" "store" "marketplace"
_Example_: "plugin"

- `plans` *string or null*
Comma separated IDs of plans the coupon would work for. If `null`, coupon supports all plans.
_Example_: "123,654,8757"

- `licenses` *string or null*
Comma separated licenses quota limits. If `null`, coupon supports all license limits. `0` is used for an unlimited-site license.
_Example_: "1,5,10,0"

- `billing_cycles` *string or null*
Comma separated billing cycles. If `null`, coupon supports all billing cycles. `0` is used for lifetime billing cycle.
_Example_: "1,12"

- `code` *string*
The coupon code.
_Example_: "BLACKFRIDAY2024"

- `discount` *integer*
The discount amount.

- `discount_type` *string*
The type of the discount. "percentage" means the discount is a percentage of the price, "dollar" means the discount is a fixed amount.
Enum "dollar" "percentage"

- `start_date` *string* *(date-time)*
Date and time from when the coupon will be activated.
_Example_: "2025-04-01 11:13:28"

- `end_date` *string* *(date-time)*
Date and time, after which the coupon will be expired.
_Example_: "2025-04-30 19:17:21"

- `redemptions` *integer*
The total number of redemptions of this coupon.

- `redemptions_limit` *integer or null*
The total number of redemptions limit of this coupon.

- `has_renewals_discount` *boolean*
Whether the coupon also supports discount for renewals or first payment only.

- `has_addons_discount` *boolean*
Whether the coupon supports discount for add-ons or not.

- `is_one_per_user` *boolean*
Whether to limit the coupon usage one per user.

- `is_active` *boolean*
Whether the coupon is active. Use this flag to temporarily disable the coupon.

- `user_type` *string*
The user type the coupon is applicable to.
_Default_: "all"
Enum "all" "new" "current" "previous" "customer" "migrated"

- `source` *number*
The source of the migration data. To get support migrating from other platform please see our [documentation](https://freemius.com/help/documentation/migration/).
    - `0` - Freemius
    - `1` - Other
    - `2` - Easy Digital Downloads (EDD)
    - `3` - WooCommerce (WC)
    - `4` - Rating Widget
    - `5` - Gumroad
    - `6` - CodeCanyon
    - `7` - ThemeForest
    - `8` - AppSumo
    - `9` - SendOwl
    - `10` - WHMCS
    - `11` - Lemon Squeezy
Enum "0" "1" "2" "3" "4" "5" "6" "7" "8" "9" "10" "11"

## Event

API response:

```json
{
  "id": "123456",
  "created": "2025-01-01 00:00:00",
  "updated": "2025-01-01 00:00:00",
  "type": "license.activated",
  "developer_id": "1234",
  "plugin_id": "123456",
  "user_id": "123456",
  "install_id": "123456",
  "data": null,
  "event_trigger": "system",
  "process_time": "2025-01-01 12:00:00",
  "objects": {
    "user": {
      "id": "1234567",
      "email": "<EMAIL>",
      "first": "Joe",
      "last": "Doe",
      "is_verified": true
    },
    "install": {
      "id": "1234567",
      "plugin_id": "12345",
      "user_id": "1234567",
      "url": "https://example.com",
      "plan_id": "12345"
    },
    "license": {
      "plugin_id": "12345",
      "user_id": "12345",
      "plan_id": "12345",
      "pricing_id": "12345",
      "quota": 10,
      "expiration": "2025-10-01 10:11:46",
      "id": "1234567",
      "created": "2025-01-01 01:11:46",
      "updated": "2025-01-01 01:11:46"
    },
    "payment": {
      "subscription_id": "12345",
      "gross": 14.99,
      "gateway_fee": 0.41,
      "vat": 1.52,
      "is_renewal": true,
      "type": "payment",
      "user_id": "12345",
      "install_id": "12345",
      "plan_id": "12345",
      "pricing_id": "12345",
      "license_id": "12345"
    }
  }
}
```


* `Body`: application/json

- `id` *string* *(int64)* *>= 1*
The unique identifier of the entity.
_Example_: "123456"

- `created` *string* *(date-time)*
The date and time the entity was created, under UTC timezone.
_Example_: "2025-01-01 00:00:00"

- `updated` *string or null* *(date-time)*
The date and time the entity was updated, under UTC timezone. If `null` then the entity was never updated since its creation.
_Example_: "2025-01-01 00:00:00"

- `type` *string*
The type of event. See our documented list of the available event types.
_Example_: "license.activated"

- `developer_id` *string* *(int64)* *>= 1*
The ID of the developer.
_Example_: "1234"

- `plugin_id` *string or null* *(int64)* *>= 1*
The ID of the product the entity belongs to. Null means it has not been associated with a product yet.
_Example_: "123456"

- `user_id` *string or null* *(int64)* *>= 1*
The ID of the user the entity belongs to. If NULL then still not associated to any user.
_Example_: "123456"

- `install_id` *string or null* *(int64)* *>= 1*
The ID of the installation or site the entity is associated with, a `null` value means it has not been associated with an installation yet.
_Example_: "123456"

- `data` *any or null*
The details of the triggered event. This can be a `string` showing ID of the associated entity, an `object` with additional information of the event, or array of objects.

- `event_trigger` *string*
The type of trigger for the event.
Enum "system" "developer" "plugin" "user" "install"
_Example_: "system"

- `process_time` *string or null* *(date-time)*
The time the event was processed. If it is `null`, the event is pending.
_Example_: "2025-01-01 12:00:00"

- `objects` *object*
A map of related objects keyed by their type or ID, such as Payment, User, Install, or License.
_Example_: 
```json
{"user":{"id":"1234567","email":"<EMAIL>","first":"Joe","last":"Doe","is_verified":true},"install":{"id":"1234567","plugin_id":"12345","user_id":"1234567","url":"https://example.com","plan_id":"12345"},"license":{"plugin_id":"12345","user_id":"12345","plan_id":"12345","pricing_id":"12345","quota":10,"expiration":"2025-10-01 10:11:46","id":"1234567","created":"2025-01-01 01:11:46","updated":"2025-01-01 01:11:46"},"payment":{"subscription_id":"12345","gross":14.99,"gateway_fee":0.41,"vat":1.52,"is_renewal":true,"type":"payment","user_id":"12345","install_id":"12345","plan_id":"12345","pricing_id":"12345","license_id":"12345"}}
```